/**
 * Controller Test Setup
 * 
 * This file contains global setup for all controller tests.
 * It configures the testing environment and provides common utilities.
 */

import 'reflect-metadata';

// 扩展 Jest 匹配器
import '@testing-library/jest-dom';

// 设置测试超时
jest.setTimeout(30000);

// 全局测试配置
beforeAll(async () => {
    // 设置测试环境变量
    process.env.NODE_ENV = 'test';
    process.env.APP_PORT = '3001'; // 使用不同的端口避免冲突
    process.env.DB_HOST = process.env.TEST_DB_HOST || 'localhost';
    process.env.DB_PORT = process.env.TEST_DB_PORT || '3306';
    process.env.DB_DATABASE = process.env.TEST_DB_DATABASE || 'nestapp_test';
    process.env.DB_USERNAME = process.env.TEST_DB_USERNAME || 'root';
    process.env.DB_PASSWORD = process.env.TEST_DB_PASSWORD || '';
    
    // 禁用日志输出（可选）
    if (process.env.DISABLE_TEST_LOGS === 'true') {
        console.log = jest.fn();
        console.warn = jest.fn();
        console.error = jest.fn();
    }
});

// 每个测试后的清理
afterEach(async () => {
    // 清理模拟
    jest.clearAllMocks();
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

// 自定义匹配器
expect.extend({
    toBeValidUUID(received: string) {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        const pass = uuidRegex.test(received);
        
        if (pass) {
            return {
                message: () => `expected ${received} not to be a valid UUID`,
                pass: true,
            };
        } else {
            return {
                message: () => `expected ${received} to be a valid UUID`,
                pass: false,
            };
        }
    },
    
    toBeValidDate(received: string | Date) {
        const date = new Date(received);
        const pass = !isNaN(date.getTime());
        
        if (pass) {
            return {
                message: () => `expected ${received} not to be a valid date`,
                pass: true,
            };
        } else {
            return {
                message: () => `expected ${received} to be a valid date`,
                pass: false,
            };
        }
    },
    
    toHaveValidPagination(received: any) {
        const hasItems = Array.isArray(received.items);
        const hasMeta = received.meta && typeof received.meta === 'object';
        const hasPage = typeof received.meta?.page === 'number';
        const hasLimit = typeof received.meta?.limit === 'number';
        const hasTotal = typeof received.meta?.total === 'number';
        
        const pass = hasItems && hasMeta && hasPage && hasLimit && hasTotal;
        
        if (pass) {
            return {
                message: () => `expected response not to have valid pagination structure`,
                pass: true,
            };
        } else {
            return {
                message: () => `expected response to have valid pagination structure with items array and meta object containing page, limit, and total`,
                pass: false,
            };
        }
    },
    
    toHaveValidErrorResponse(received: any, expectedStatusCode?: number) {
        const hasMessage = received.message !== undefined;
        const hasError = typeof received.error === 'string';
        const hasStatusCode = typeof received.statusCode === 'number';
        const statusCodeMatches = expectedStatusCode ? received.statusCode === expectedStatusCode : true;
        
        const pass = hasMessage && hasError && hasStatusCode && statusCodeMatches;
        
        if (pass) {
            return {
                message: () => `expected response not to be a valid error response`,
                pass: true,
            };
        } else {
            return {
                message: () => `expected response to be a valid error response with message, error, and statusCode${expectedStatusCode ? ` (${expectedStatusCode})` : ''}`,
                pass: false,
            };
        }
    }
});

// 声明自定义匹配器的类型
declare global {
    namespace jest {
        interface Matchers<R> {
            toBeValidUUID(): R;
            toBeValidDate(): R;
            toHaveValidPagination(): R;
            toHaveValidErrorResponse(expectedStatusCode?: number): R;
        }
    }
}

// 测试工具函数
export const testUtils = {
    /**
     * 等待指定时间
     */
    delay: (ms: number): Promise<void> => {
        return new Promise(resolve => setTimeout(resolve, ms));
    },
    
    /**
     * 生成随机字符串
     */
    randomString: (length: number = 10): string => {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },
    
    /**
     * 生成随机数字
     */
    randomNumber: (min: number = 0, max: number = 100): number => {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    /**
     * 生成随机邮箱
     */
    randomEmail: (): string => {
        return `test_${testUtils.randomString(8)}@example.com`;
    },
    
    /**
     * 验证响应结构
     */
    validateResponse: (response: any, expectedFields: string[]): boolean => {
        return expectedFields.every(field => response.hasOwnProperty(field));
    },
    
    /**
     * 验证分页响应
     */
    validatePaginationResponse: (response: any): boolean => {
        return (
            Array.isArray(response.items) &&
            response.meta &&
            typeof response.meta.page === 'number' &&
            typeof response.meta.limit === 'number' &&
            typeof response.meta.total === 'number'
        );
    },
    
    /**
     * 验证错误响应
     */
    validateErrorResponse: (response: any, expectedStatusCode?: number): boolean => {
        const hasRequiredFields = (
            response.message !== undefined &&
            typeof response.error === 'string' &&
            typeof response.statusCode === 'number'
        );
        
        if (expectedStatusCode) {
            return hasRequiredFields && response.statusCode === expectedStatusCode;
        }
        
        return hasRequiredFields;
    }
};

// 导出测试工具
export default testUtils;
