#!/usr/bin/env ts-node

/**
 * Controller Tests Runner
 * 
 * This script helps run all controller tests individually or together.
 * Usage:
 *   npm run test:controllers              # Run all controller tests
 *   npm run test:controllers category     # Run only category controller tests
 *   npm run test:controllers manager      # Run only manager controller tests
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

const testDir = __dirname;

// 定义所有的测试文件
const testFiles = {
    // App controllers
    'category': 'controllers/category.controller.test.ts',
    'tag': 'controllers/tag.controller.test.ts',
    'post': 'controllers/post.controller.test.ts',
    'comment': 'controllers/comment.controller.test.ts',
    'user': 'controllers/user.controller.test.ts',
    'account': 'controllers/account.controller.test.ts',
    'role': 'controllers/role.controller.test.ts',
    
    // Manager controllers
    'manager-category': 'controllers/manager/category.controller.test.ts',
    'manager-tag': 'controllers/manager/tag.controller.test.ts',
    'manager-post': 'controllers/manager/post.controller.test.ts',
    'manager-comment': 'controllers/manager/comment.controller.test.ts',
    'manager-user': 'controllers/manager/user.controller.test.ts',
    'manager-role': 'controllers/manager/role.controller.test.ts',
    'manager-permission': 'controllers/manager/permission.controller.test.ts',
};

// 测试组
const testGroups = {
    'app': [
        'category', 'tag', 'post', 'comment', 'user', 'account', 'role'
    ],
    'manager': [
        'manager-category', 'manager-tag', 'manager-post', 'manager-comment', 
        'manager-user', 'manager-role', 'manager-permission'
    ],
    'content': [
        'category', 'tag', 'post', 'comment', 
        'manager-category', 'manager-tag', 'manager-post', 'manager-comment'
    ],
    'user-auth': [
        'user', 'account', 'manager-user'
    ],
    'rbac': [
        'role', 'manager-role', 'manager-permission'
    ]
};

function runTest(testFile: string): Promise<number> {
    return new Promise((resolve) => {
        const testPath = join(testDir, testFile);
        
        if (!existsSync(testPath)) {
            console.error(`❌ Test file not found: ${testPath}`);
            resolve(1);
            return;
        }

        console.log(`🧪 Running test: ${testFile}`);
        
        const jest = spawn('npx', ['jest', testPath, '--verbose'], {
            stdio: 'inherit',
            cwd: join(testDir, '..'),
        });

        jest.on('close', (code) => {
            if (code === 0) {
                console.log(`✅ Test passed: ${testFile}`);
            } else {
                console.log(`❌ Test failed: ${testFile}`);
            }
            resolve(code || 0);
        });

        jest.on('error', (error) => {
            console.error(`❌ Error running test ${testFile}:`, error);
            resolve(1);
        });
    });
}

async function runTests(testNames: string[]): Promise<void> {
    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;

    console.log(`🚀 Starting controller tests...`);
    console.log(`📋 Tests to run: ${testNames.length}`);
    console.log('');

    for (const testName of testNames) {
        const testFile = testFiles[testName];
        if (!testFile) {
            console.error(`❌ Unknown test: ${testName}`);
            continue;
        }

        totalTests++;
        const result = await runTest(testFile);
        
        if (result === 0) {
            passedTests++;
        } else {
            failedTests++;
        }
        
        console.log(''); // 空行分隔
    }

    // 输出总结
    console.log('='.repeat(50));
    console.log('📊 Test Summary:');
    console.log(`   Total: ${totalTests}`);
    console.log(`   ✅ Passed: ${passedTests}`);
    console.log(`   ❌ Failed: ${failedTests}`);
    console.log('='.repeat(50));

    if (failedTests > 0) {
        process.exit(1);
    }
}

function showHelp(): void {
    console.log('Controller Tests Runner');
    console.log('');
    console.log('Usage:');
    console.log('  npm run test:controllers [test-name|group-name]');
    console.log('');
    console.log('Available tests:');
    Object.keys(testFiles).forEach(name => {
        console.log(`  ${name.padEnd(20)} - ${testFiles[name]}`);
    });
    console.log('');
    console.log('Available groups:');
    Object.keys(testGroups).forEach(group => {
        console.log(`  ${group.padEnd(20)} - ${testGroups[group].join(', ')}`);
    });
    console.log('');
    console.log('Examples:');
    console.log('  npm run test:controllers                    # Run all tests');
    console.log('  npm run test:controllers category           # Run category controller test');
    console.log('  npm run test:controllers app               # Run all app controller tests');
    console.log('  npm run test:controllers manager           # Run all manager controller tests');
    console.log('  npm run test:controllers content           # Run all content-related tests');
}

async function main(): Promise<void> {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        showHelp();
        return;
    }

    let testsToRun: string[] = [];

    if (args.length === 0) {
        // 运行所有测试
        testsToRun = Object.keys(testFiles);
    } else {
        for (const arg of args) {
            if (testGroups[arg]) {
                // 运行测试组
                testsToRun.push(...testGroups[arg]);
            } else if (testFiles[arg]) {
                // 运行单个测试
                testsToRun.push(arg);
            } else {
                console.error(`❌ Unknown test or group: ${arg}`);
                console.log('Use --help to see available options');
                process.exit(1);
            }
        }
    }

    // 去重
    testsToRun = [...new Set(testsToRun)];

    await runTests(testsToRun);
}

// 运行主函数
main().catch((error) => {
    console.error('❌ Error:', error);
    process.exit(1);
});
