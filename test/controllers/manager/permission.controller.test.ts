import { NestFastifyApplication } from '@nestjs/platform-fastify';
import { DataSource } from 'typeorm';

import { PermissionEntity } from '@/modules/rbac/entities';
import { PermissionRepository } from '@/modules/rbac/repositories';
import { UserEntity } from '@/modules/user/entities';
import { UserRepository } from '@/modules/user/repositories';
import { createApp } from '@/modules/core/helpers/app';
import { App } from '@/modules/core/types';

import { createOptions } from '@/options';

const URL_PREFIX = '/api/v1/manager/rbac';

describe('PermissionController (Manager)', () => {
    let datasource: DataSource;
    let app: NestFastifyApplication;
    let permissionRepository: PermissionRepository;
    let userRepository: UserRepository;
    let testPermissions: PermissionEntity[];
    let adminUser: UserEntity;
    let authToken: string;

    beforeAll(async () => {
        const appConfig: App = await createApp(createOptions)();
        app = appConfig.container;
        await app.init();
        await app.getHttpAdapter().getInstance().ready();

        permissionRepository = app.get<PermissionRepository>(PermissionRepository);
        userRepository = app.get<UserRepository>(UserRepository);
        datasource = app.get<DataSource>(DataSource);
        
        if (!datasource.isInitialized) {
            await datasource.initialize();
        }

        // 创建测试数据
        await setupTestData();
    });

    afterAll(async () => {
        // 清理测试数据
        await cleanupTestData();
        await datasource.destroy();
        await app.close();
    });

    async function setupTestData() {
        // 创建管理员用户
        adminUser = await userRepository.save({
            username: 'admin_permission_manager',
            nickname: 'Admin Permission Manager',
            password: 'password123',
        });

        // 获取认证token
        const loginResult = await app.inject({
            method: 'POST',
            url: '/api/v1/user/account/login',
            body: {
                credential: 'admin_permission_manager',
                password: 'password123',
            },
        });
        authToken = loginResult.json().token;

        // 创建测试权限数据
        const permission1 = await permissionRepository.save({
            name: 'Manager Test Permission 1',
            label: 'manager.test.permission.1',
            description: 'Manager test permission description 1',
        });

        const permission2 = await permissionRepository.save({
            name: 'Manager Test Permission 2',
            label: 'manager.test.permission.2',
            description: 'Manager test permission description 2',
        });

        const permission3 = await permissionRepository.save({
            name: 'Manager Test Permission 3',
            label: 'manager.test.permission.3',
        });

        testPermissions = [permission1, permission2, permission3];
    }

    async function cleanupTestData() {
        if (testPermissions && testPermissions.length > 0) {
            await permissionRepository.remove(testPermissions);
        }
        if (adminUser) {
            await userRepository.remove(adminUser);
        }
    }

    describe('GET /permissions', () => {
        it('should require authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions`,
            });

            expect(result.statusCode).toBe(401);
        });

        it('should return paginated permissions with authentication', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
            });

            expect(result.statusCode).toBe(200);
            const response = result.json();
            expect(response.items).toBeDefined();
            expect(response.meta).toBeDefined();
            expect(Array.isArray(response.items)).toBe(true);
        });

        it('should handle pagination parameters', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?page=1&limit=5`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
            });

            expect(result.statusCode).toBe(200);
            const response = result.json();
            expect(response.meta.page).toBe(1);
            expect(response.meta.limit).toBe(5);
        });

        it('should search permissions by name', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?search=Manager Test Permission 1`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
            });

            expect(result.statusCode).toBe(200);
            const response = result.json();
            
            if (response.items.length > 0) {
                const foundPermission = response.items.find((permission: any) => 
                    permission.name.includes('Manager Test Permission 1')
                );
                expect(foundPermission).toBeDefined();
            }
        });

        it('should fail with invalid token', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: 'Bearer invalid-token',
                },
            });

            expect(result.statusCode).toBe(401);
        });

        it('should handle invalid pagination parameters', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions?page=0&limit=0`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
            });

            expect(result.statusCode).toBe(400);
        });
    });

    describe('GET /permissions/:id', () => {
        it('should require authentication', async () => {
            const permission = testPermissions[0];
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/${permission.id}`,
            });

            expect(result.statusCode).toBe(401);
        });

        it('should return permission detail with authentication', async () => {
            const permission = testPermissions[0];
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/${permission.id}`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
            });

            expect(result.statusCode).toBe(200);
            const permissionDetail = result.json();
            expect(permissionDetail.id).toBe(permission.id);
            expect(permissionDetail.name).toBe(permission.name);
            expect(permissionDetail.label).toBe(permission.label);
            expect(permissionDetail.description).toBe(permission.description);
        });

        it('should return permission without description if not set', async () => {
            const permissionWithoutDesc = testPermissions.find(p => !p.description);
            if (permissionWithoutDesc) {
                const result = await app.inject({
                    method: 'GET',
                    url: `${URL_PREFIX}/permissions/${permissionWithoutDesc.id}`,
                    headers: {
                        authorization: `Bearer ${authToken}`,
                    },
                });

                expect(result.statusCode).toBe(200);
                const permissionDetail = result.json();
                expect(permissionDetail.id).toBe(permissionWithoutDesc.id);
                expect(permissionDetail.name).toBe(permissionWithoutDesc.name);
                expect(permissionDetail.label).toBe(permissionWithoutDesc.label);
            }
        });

        it('should fail with invalid UUID', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/invalid-uuid`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
            });

            expect(result.statusCode).toBe(400);
        });

        it('should fail with non-existent permission ID', async () => {
            const result = await app.inject({
                method: 'GET',
                url: `${URL_PREFIX}/permissions/74e655b3-b69a-42ae-a101-41c224386e74`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
            });

            expect(result.statusCode).toBe(404);
        });
    });

    describe('POST /permissions', () => {
        it('should require authentication', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/permissions`,
                body: {
                    name: 'New Test Permission',
                    label: 'new.test.permission',
                },
            });

            expect(result.statusCode).toBe(401);
        });

        it('should create permission successfully', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    name: 'New Manager Test Permission',
                    label: 'new.manager.test.permission',
                    description: 'New manager test permission description',
                },
            });

            expect(result.statusCode).toBe(201);
            const newPermission = result.json();
            expect(newPermission.name).toBe('New Manager Test Permission');
            expect(newPermission.label).toBe('new.manager.test.permission');
            expect(newPermission.description).toBe('New manager test permission description');
            
            // 清理创建的权限
            await permissionRepository.delete(newPermission.id);
        });

        it('should create permission without description', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    name: 'Permission Without Description',
                    label: 'permission.without.description',
                },
            });

            expect(result.statusCode).toBe(201);
            const newPermission = result.json();
            expect(newPermission.name).toBe('Permission Without Description');
            expect(newPermission.label).toBe('permission.without.description');
            
            // 清理创建的权限
            await permissionRepository.delete(newPermission.id);
        });

        it('should fail with missing name', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    label: 'test.permission.label',
                    description: 'Permission description without name',
                },
            });

            expect(result.statusCode).toBe(400);
            expect(result.json().message).toContain('The permission name cannot be empty');
        });

        it('should fail with missing label', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    name: 'Test Permission Name',
                    description: 'Permission description without label',
                },
            });

            expect(result.statusCode).toBe(400);
            expect(result.json().message).toContain('The permission label cannot be empty');
        });

        it('should fail with duplicate name', async () => {
            const existingPermission = testPermissions[0];
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    name: existingPermission.name,
                    label: 'different.label',
                },
            });

            expect(result.statusCode).toBe(400);
            expect(result.json().message).toContain('The permission names are repeated');
        });

        it('should fail with duplicate label', async () => {
            const existingPermission = testPermissions[0];
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    name: 'Different Permission Name',
                    label: existingPermission.label,
                },
            });

            expect(result.statusCode).toBe(400);
            expect(result.json().message).toContain('The permission labels are repeated');
        });

        it('should fail with invalid label format', async () => {
            const result = await app.inject({
                method: 'POST',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    name: 'Test Permission',
                    label: 'Invalid Label With Spaces',
                },
            });

            expect(result.statusCode).toBe(400);
            expect(result.json().message).toContain('The permission label format is incorrect');
        });
    });

    describe('PATCH /permissions', () => {
        it('should require authentication', async () => {
            const permission = testPermissions[0];
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/permissions`,
                body: {
                    id: permission.id,
                    name: 'Updated Permission Name',
                },
            });

            expect(result.statusCode).toBe(401);
        });

        it('should update permission successfully', async () => {
            const permission = testPermissions[0];
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    id: permission.id,
                    name: 'Updated Manager Permission',
                    description: 'Updated manager permission description',
                },
            });

            expect(result.statusCode).toBe(200);
            const updatedPermission = result.json();
            expect(updatedPermission.name).toBe('Updated Manager Permission');
            expect(updatedPermission.description).toBe('Updated manager permission description');
        });

        it('should fail with missing ID', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    name: 'Updated Permission',
                },
            });

            expect(result.statusCode).toBe(400);
            expect(result.json().message).toContain('The ID must be specified');
        });

        it('should fail with invalid ID format', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    id: 'invalid-uuid',
                    name: 'Updated Permission',
                },
            });

            expect(result.statusCode).toBe(400);
        });

        it('should fail with non-existent ID', async () => {
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    id: '74e655b3-b69a-42ae-a101-41c224386e74',
                    name: 'Updated Permission',
                },
            });

            expect(result.statusCode).toBe(400);
        });

        it('should fail with duplicate name', async () => {
            const [permission1, permission2] = testPermissions;
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    id: permission1.id,
                    name: permission2.name, // 使用另一个权限的名称
                },
            });

            expect(result.statusCode).toBe(400);
            expect(result.json().message).toContain('The permission names are repeated');
        });

        it('should fail with duplicate label', async () => {
            const [permission1, permission2] = testPermissions;
            const result = await app.inject({
                method: 'PATCH',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    id: permission1.id,
                    label: permission2.label, // 使用另一个权限的标签
                },
            });

            expect(result.statusCode).toBe(400);
            expect(result.json().message).toContain('The permission labels are repeated');
        });
    });

    describe('DELETE /permissions', () => {
        it('should require authentication', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/permissions`,
                body: {
                    ids: [testPermissions[0].id],
                },
            });

            expect(result.statusCode).toBe(401);
        });

        it('should delete permissions successfully', async () => {
            // 创建临时权限用于删除测试
            const tempPermission = await permissionRepository.save({
                name: 'Temp Permission for Delete',
                label: 'temp.permission.for.delete',
                description: 'Temporary permission for deletion test',
            });

            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    ids: [tempPermission.id],
                },
            });

            expect(result.statusCode).toBe(200);

            // 验证权限已被删除
            const deletedPermission = await permissionRepository.findOne({ where: { id: tempPermission.id } });
            expect(deletedPermission).toBeNull();
        });

        it('should delete multiple permissions successfully', async () => {
            // 创建多个临时权限用于删除测试
            const tempPermission1 = await permissionRepository.save({
                name: 'Temp Permission 1 for Delete',
                label: 'temp.permission.1.for.delete',
            });
            const tempPermission2 = await permissionRepository.save({
                name: 'Temp Permission 2 for Delete',
                label: 'temp.permission.2.for.delete',
            });

            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    ids: [tempPermission1.id, tempPermission2.id],
                },
            });

            expect(result.statusCode).toBe(200);

            // 验证权限已被删除
            const deletedPermission1 = await permissionRepository.findOne({ where: { id: tempPermission1.id } });
            const deletedPermission2 = await permissionRepository.findOne({ where: { id: tempPermission2.id } });
            expect(deletedPermission1).toBeNull();
            expect(deletedPermission2).toBeNull();
        });

        it('should fail with missing ids', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {},
            });

            expect(result.statusCode).toBe(400);
        });

        it('should fail with empty ids array', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    ids: [],
                },
            });

            expect(result.statusCode).toBe(400);
        });

        it('should fail with invalid UUID in ids', async () => {
            const result = await app.inject({
                method: 'DELETE',
                url: `${URL_PREFIX}/permissions`,
                headers: {
                    authorization: `Bearer ${authToken}`,
                },
                body: {
                    ids: ['invalid-uuid'],
                },
            });

            expect(result.statusCode).toBe(400);
        });
    });
});
