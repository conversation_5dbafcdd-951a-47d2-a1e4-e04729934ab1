/**
 * Jest Configuration for Controller Tests
 * 
 * This configuration is specifically designed for controller tests.
 * It includes proper setup for NestJS testing environment.
 */

module.exports = {
    // 基础配置
    preset: 'ts-jest',
    testEnvironment: 'node',
    
    // 测试文件匹配模式
    testMatch: [
        '<rootDir>/test/controllers/**/*.test.ts',
        '<rootDir>/test/controllers/**/*.spec.ts'
    ],
    
    // 忽略的文件和目录
    testPathIgnorePatterns: [
        '/node_modules/',
        '/dist/',
        '/coverage/'
    ],
    
    // 模块路径映射（与 tsconfig.json 保持一致）
    moduleNameMapping: {
        '^@/(.*)$': '<rootDir>/src/$1'
    },
    
    // 设置文件
    setupFilesAfterEnv: [
        '<rootDir>/test/setup/controller-test-setup.ts'
    ],
    
    // 覆盖率配置
    collectCoverage: true,
    collectCoverageFrom: [
        'src/modules/**/controllers/**/*.ts',
        '!src/modules/**/controllers/**/index.ts',
        '!src/**/*.d.ts',
        '!src/**/*.interface.ts',
        '!src/**/*.type.ts'
    ],
    coverageDirectory: '<rootDir>/coverage/controllers',
    coverageReporters: [
        'text',
        'text-summary',
        'html',
        'lcov'
    ],
    
    // 测试超时设置
    testTimeout: 30000, // 30秒
    
    // 全局变量
    globals: {
        'ts-jest': {
            tsconfig: '<rootDir>/tsconfig.json'
        }
    },
    
    // 模块文件扩展名
    moduleFileExtensions: [
        'js',
        'json',
        'ts'
    ],
    
    // 转换配置
    transform: {
        '^.+\\.(t|j)s$': 'ts-jest'
    },
    
    // 详细输出
    verbose: true,
    
    // 错误时停止
    bail: false,
    
    // 并行运行
    maxWorkers: '50%',
    
    // 缓存
    cache: true,
    cacheDirectory: '<rootDir>/.jest-cache',
    
    // 清除模拟
    clearMocks: true,
    restoreMocks: true,
    
    // 报告器
    reporters: [
        'default',
        [
            'jest-html-reporters',
            {
                publicPath: '<rootDir>/coverage/controllers',
                filename: 'test-report.html',
                expand: true,
                hideIcon: false,
                pageTitle: 'Controller Tests Report'
            }
        ]
    ],
    
    // 测试结果处理器
    testResultsProcessor: '<rootDir>/test/helpers/test-results-processor.js'
};
