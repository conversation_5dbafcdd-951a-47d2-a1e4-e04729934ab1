/**
 * Test Results Processor
 * 
 * This processor formats and enhances test results for better readability.
 */

const fs = require('fs');
const path = require('path');

module.exports = (results) => {
    // 生成测试报告摘要
    const summary = {
        timestamp: new Date().toISOString(),
        totalTests: results.numTotalTests,
        passedTests: results.numPassedTests,
        failedTests: results.numFailedTests,
        pendingTests: results.numPendingTests,
        totalTestSuites: results.numTotalTestSuites,
        passedTestSuites: results.numPassedTestSuites,
        failedTestSuites: results.numFailedTestSuites,
        testResults: []
    };

    // 处理每个测试套件的结果
    results.testResults.forEach(testResult => {
        const suiteInfo = {
            testFilePath: testResult.testFilePath,
            numPassingTests: testResult.numPassingTests,
            numFailingTests: testResult.numFailingTests,
            numPendingTests: testResult.numPendingTests,
            perfStats: testResult.perfStats,
            testResults: []
        };

        // 处理每个测试用例
        testResult.testResults.forEach(test => {
            const testInfo = {
                title: test.title,
                fullName: test.fullName,
                status: test.status,
                duration: test.duration,
                ancestorTitles: test.ancestorTitles
            };

            // 如果测试失败，添加错误信息
            if (test.status === 'failed' && test.failureMessages) {
                testInfo.failureMessages = test.failureMessages;
            }

            suiteInfo.testResults.push(testInfo);
        });

        summary.testResults.push(suiteInfo);
    });

    // 保存详细报告到文件
    const reportDir = path.join(process.cwd(), 'coverage', 'controllers');
    if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
    }

    const reportPath = path.join(reportDir, 'test-results.json');
    fs.writeFileSync(reportPath, JSON.stringify(summary, null, 2));

    // 生成简化的控制台输出
    console.log('\n' + '='.repeat(80));
    console.log('📊 CONTROLLER TESTS SUMMARY');
    console.log('='.repeat(80));
    console.log(`📅 Timestamp: ${summary.timestamp}`);
    console.log(`📁 Test Suites: ${summary.passedTestSuites}/${summary.totalTestSuites} passed`);
    console.log(`🧪 Tests: ${summary.passedTests}/${summary.totalTests} passed`);
    
    if (summary.failedTests > 0) {
        console.log(`❌ Failed Tests: ${summary.failedTests}`);
    }
    
    if (summary.pendingTests > 0) {
        console.log(`⏸️  Pending Tests: ${summary.pendingTests}`);
    }

    // 显示失败的测试详情
    if (summary.failedTests > 0) {
        console.log('\n' + '='.repeat(80));
        console.log('❌ FAILED TESTS DETAILS');
        console.log('='.repeat(80));

        summary.testResults.forEach(suite => {
            const failedTests = suite.testResults.filter(test => test.status === 'failed');
            if (failedTests.length > 0) {
                console.log(`\n📁 ${path.basename(suite.testFilePath)}`);
                failedTests.forEach(test => {
                    console.log(`  ❌ ${test.fullName}`);
                    if (test.failureMessages && test.failureMessages.length > 0) {
                        // 只显示第一个错误信息的前几行
                        const errorLines = test.failureMessages[0].split('\n').slice(0, 3);
                        errorLines.forEach(line => {
                            if (line.trim()) {
                                console.log(`     ${line.trim()}`);
                            }
                        });
                    }
                });
            }
        });
    }

    // 显示性能统计
    console.log('\n' + '='.repeat(80));
    console.log('⚡ PERFORMANCE STATS');
    console.log('='.repeat(80));

    let totalDuration = 0;
    let slowestTest = null;
    let slowestSuite = null;

    summary.testResults.forEach(suite => {
        const suiteDuration = suite.perfStats.end - suite.perfStats.start;
        totalDuration += suiteDuration;

        if (!slowestSuite || suiteDuration > (slowestSuite.duration || 0)) {
            slowestSuite = {
                name: path.basename(suite.testFilePath),
                duration: suiteDuration
            };
        }

        suite.testResults.forEach(test => {
            if (test.duration && (!slowestTest || test.duration > slowestTest.duration)) {
                slowestTest = {
                    name: test.fullName,
                    duration: test.duration,
                    suite: path.basename(suite.testFilePath)
                };
            }
        });
    });

    console.log(`⏱️  Total Duration: ${totalDuration}ms`);
    if (slowestSuite) {
        console.log(`🐌 Slowest Suite: ${slowestSuite.name} (${slowestSuite.duration}ms)`);
    }
    if (slowestTest) {
        console.log(`🐌 Slowest Test: ${slowestTest.name} (${slowestTest.duration}ms)`);
    }

    // 显示覆盖率信息（如果可用）
    if (results.coverageMap) {
        console.log('\n' + '='.repeat(80));
        console.log('📈 COVERAGE SUMMARY');
        console.log('='.repeat(80));
        console.log('📊 Detailed coverage report available in coverage/controllers/');
    }

    console.log('\n' + '='.repeat(80));
    console.log(`📄 Detailed report saved to: ${reportPath}`);
    console.log('='.repeat(80));

    // 返回原始结果
    return results;
};
