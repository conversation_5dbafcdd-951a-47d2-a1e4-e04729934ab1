/**
 * Test Data Generator for Controller Tests
 * 
 * This module provides utilities to generate test data for controller tests.
 * Each test should use isolated data to avoid conflicts between tests.
 */

import { faker } from '@faker-js/faker';

export interface TestUserData {
    username: string;
    nickname: string;
    password: string;
    email?: string;
}

export interface TestCategoryData {
    name: string;
    customOrder?: number;
    parent?: string;
}

export interface TestTagData {
    name: string;
    desc?: string;
}

export interface TestPostData {
    title: string;
    body: string;
    summary?: string;
    keywords?: string[];
    customOrder?: number;
    publishedAt?: Date | null;
}

export interface TestCommentData {
    body: string;
    post?: string;
    parent?: string;
}

export interface TestRoleData {
    name: string;
    label: string;
    description?: string;
}

/**
 * 生成唯一的测试用户数据
 */
export function generateTestUser(prefix: string = 'testuser'): TestUserData {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    
    return {
        username: `${prefix}_${timestamp}_${random}`,
        nickname: faker.person.fullName(),
        password: 'password123',
        email: faker.internet.email(),
    };
}

/**
 * 生成多个测试用户
 */
export function generateTestUsers(count: number, prefix: string = 'testuser'): TestUserData[] {
    return Array.from({ length: count }, (_, index) => 
        generateTestUser(`${prefix}_${index}`)
    );
}

/**
 * 生成唯一的测试分类数据
 */
export function generateTestCategory(prefix: string = 'TestCategory'): TestCategoryData {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    
    return {
        name: `${prefix}_${timestamp}_${random}`,
        customOrder: Math.floor(Math.random() * 100) + 1,
    };
}

/**
 * 生成多个测试分类
 */
export function generateTestCategories(count: number, prefix: string = 'TestCategory'): TestCategoryData[] {
    return Array.from({ length: count }, (_, index) => 
        generateTestCategory(`${prefix}_${index}`)
    );
}

/**
 * 生成分类树结构数据
 */
export function generateCategoryTree(depth: number = 2, childrenPerLevel: number = 2): TestCategoryData[] {
    const categories: TestCategoryData[] = [];
    
    function createLevel(level: number, parentName?: string): void {
        if (level > depth) return;
        
        for (let i = 0; i < childrenPerLevel; i++) {
            const category = generateTestCategory(`Category_L${level}_${i}`);
            if (parentName) {
                category.name = `${parentName}_Child_${i}`;
            }
            categories.push(category);
            
            if (level < depth) {
                createLevel(level + 1, category.name);
            }
        }
    }
    
    createLevel(1);
    return categories;
}

/**
 * 生成唯一的测试标签数据
 */
export function generateTestTag(prefix: string = 'TestTag'): TestTagData {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    
    return {
        name: `${prefix}_${timestamp}_${random}`,
        desc: faker.lorem.sentence(),
    };
}

/**
 * 生成多个测试标签
 */
export function generateTestTags(count: number, prefix: string = 'TestTag'): TestTagData[] {
    return Array.from({ length: count }, (_, index) => 
        generateTestTag(`${prefix}_${index}`)
    );
}

/**
 * 生成唯一的测试文章数据
 */
export function generateTestPost(prefix: string = 'TestPost'): TestPostData {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    
    return {
        title: `${prefix}_${timestamp}_${random}`,
        body: faker.lorem.paragraphs(3, '\n\n'),
        summary: faker.lorem.sentence(),
        keywords: faker.lorem.words(3).split(' '),
        customOrder: Math.floor(Math.random() * 1000) + 1,
        publishedAt: Math.random() > 0.5 ? new Date() : null, // 50% 概率发布
    };
}

/**
 * 生成多个测试文章
 */
export function generateTestPosts(count: number, prefix: string = 'TestPost'): TestPostData[] {
    return Array.from({ length: count }, (_, index) => 
        generateTestPost(`${prefix}_${index}`)
    );
}

/**
 * 生成已发布的测试文章
 */
export function generatePublishedTestPost(prefix: string = 'PublishedPost'): TestPostData {
    const post = generateTestPost(prefix);
    post.publishedAt = new Date();
    return post;
}

/**
 * 生成草稿测试文章
 */
export function generateDraftTestPost(prefix: string = 'DraftPost'): TestPostData {
    const post = generateTestPost(prefix);
    post.publishedAt = null;
    return post;
}

/**
 * 生成唯一的测试评论数据
 */
export function generateTestComment(prefix: string = 'TestComment'): TestCommentData {
    return {
        body: `${prefix}: ${faker.lorem.paragraph()}`,
    };
}

/**
 * 生成多个测试评论
 */
export function generateTestComments(count: number, prefix: string = 'TestComment'): TestCommentData[] {
    return Array.from({ length: count }, (_, index) => 
        generateTestComment(`${prefix}_${index}`)
    );
}

/**
 * 生成评论树结构数据
 */
export function generateCommentTree(depth: number = 3, repliesPerLevel: number = 2): TestCommentData[] {
    const comments: TestCommentData[] = [];
    
    function createLevel(level: number, parentIndex?: number): void {
        if (level > depth) return;
        
        for (let i = 0; i < repliesPerLevel; i++) {
            const comment = generateTestComment(`Comment_L${level}_${i}`);
            comments.push(comment);
            const currentIndex = comments.length - 1;
            
            if (level < depth) {
                createLevel(level + 1, currentIndex);
            }
        }
    }
    
    createLevel(1);
    return comments;
}

/**
 * 生成唯一的测试角色数据
 */
export function generateTestRole(prefix: string = 'TestRole'): TestRoleData {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    
    return {
        name: `${prefix}_${timestamp}_${random}`,
        label: `${prefix.toLowerCase()}_${timestamp}_${random}`,
        description: faker.lorem.sentence(),
    };
}

/**
 * 生成多个测试角色
 */
export function generateTestRoles(count: number, prefix: string = 'TestRole'): TestRoleData[] {
    return Array.from({ length: count }, (_, index) => 
        generateTestRole(`${prefix}_${index}`)
    );
}

/**
 * 生成管理员用户数据
 */
export function generateAdminUser(prefix: string = 'admin'): TestUserData {
    const user = generateTestUser(prefix);
    user.nickname = `Admin ${user.nickname}`;
    return user;
}

/**
 * 生成随机数组
 */
export function getRandomItems<T>(array: T[], count: number): T[] {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, array.length));
}

/**
 * 生成随机数字
 */
export function randomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 生成随机布尔值
 */
export function randomBoolean(): boolean {
    return Math.random() > 0.5;
}

/**
 * 延迟函数，用于测试中的等待
 */
export function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 生成唯一的测试前缀
 */
export function generateTestPrefix(testName: string): string {
    const timestamp = Date.now();
    return `${testName}_${timestamp}`;
}

/**
 * 清理测试数据的辅助函数
 */
export class TestDataCleaner {
    private cleanupTasks: (() => Promise<void>)[] = [];

    addCleanupTask(task: () => Promise<void>): void {
        this.cleanupTasks.push(task);
    }

    async cleanup(): Promise<void> {
        for (const task of this.cleanupTasks.reverse()) {
            try {
                await task();
            } catch (error) {
                console.error('Cleanup task failed:', error);
            }
        }
        this.cleanupTasks = [];
    }
}

/**
 * 测试数据管理器
 */
export class TestDataManager {
    private cleaner = new TestDataCleaner();

    getCleaner(): TestDataCleaner {
        return this.cleaner;
    }

    async cleanup(): Promise<void> {
        await this.cleaner.cleanup();
    }
}
